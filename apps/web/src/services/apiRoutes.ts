const baseDoc = "/doc/v1.0";
const baseZoho = "/zoho/v1.0";
const baseFunnel = "/funnel/v1.0";
const baseChat = "/chat/v1.0";
const reportBase = "/report/v1.0";
const baseAdmin = "/admin/v1.0";
const baseRequests = "/requests/v1.0";

const routes = {
	LOGIN: "/login",
	CURRENT_USER: "/current-user",
	LOGOUT: "/logout",
	TEST: "/test",
	POST_USER: "/user",
	GET_PATIENTS: "/doc/v1.0/patients",
	GET_PATIENTS_REDIS: "/doc/v1.0/patients/redis",
	POST_PATIENT_TP: "/doc/v1.0/patient",
	POST_EMAIL: "/doc/v1.0/email",
	UPDATE_LOCK: "/doc/v1.0/lock",
	GET_LOCKED: "/doc/v1.0/locked",
	GET_PATIENT: `${baseDoc}/patient`,
	GET_PATIENT_BY_ZOHO_ID: `${baseDoc}/patient-by-id`,
	GET_PATIENT_BY_PATIENT_ID: `${baseDoc}/patient-by-patient-id`,
	GET_PATIENTS_TEST: `${baseDoc}/test/patients`,
	GET_DOCTOR: `${baseDoc}/doc`,
	GET_ALL_DOCTOR: `${baseDoc}/docs`,
	GET_ACTIVE_DOCTORS: `${baseDoc}/active-docs`,
	POST_DOCTOR: "/doc/v1.0/doc",
	GET_NEXT_PATIENT: "/doc/v1.0/next",
	POST_NOTIFY_NEXT_PATIENT: "/doc/v1.0/notify/next",
	UPDATE_MEETING_STATUS: "/doc/v1.0/meeting/patient",
	POST_AVAILABILITES: `${baseDoc}/availabilities`,
	DELETE_AVAILABILITES: `${baseDoc}/availability`,
	DELETE_AVAILABILITES_FORCE: `${baseDoc}/force/availability`,
	GET_AVAILABILITES: `${baseDoc}/availabilities`,
	GET_AVAILABILITES_ADMIN: `${baseDoc}/admin/availabilities`,
	POST_SLOT: `${baseZoho}/booking`,
	GET_BOOKING_HISTORY: `${baseZoho}/booking-history`,
	GET_ADMIN_BOOKINGS: `${baseZoho}/admin-bookings`,
	GET_LEAD_ID: `${baseDoc}/leadId`,
	POST_REDIRECT: `${baseDoc}/redirect`,
	QUEUE: `${baseDoc}/queue`,
	NO_SHOW: `${baseDoc}/noshow/check`,
	QUEUE_DETAILS_ID: `${baseDoc}/details/queue`,
	NEXTADMIT_PATIENT: `${baseDoc}/next/autoadmit`,
	LEFT_QUEUE: `${baseDoc}/left/queue`,
	COMPLETED_QUEUE: `${baseDoc}/completed/queue`,
	CONFIRMED_QUEUE: `${baseDoc}/confirmed/queue`,
	ENDED_QUEUE: `${baseDoc}/ended/queue`,
	JOINED_CALL_QUEUE: `${baseDoc}/joinedCall/queue`,
	EMPTY_QUEUE: `${baseDoc}/all/queue`,
	ONLINE_QUEUE: `${baseDoc}/online/queue`,
	OFFLINE_QUEUE: `${baseDoc}/offline`,
	JOINED_QUEUE: `${baseDoc}/joined/queue`,
	ADMISSION: `${baseDoc}/admission`,
	PATIENT_HISTORY: `${baseDoc}/search`,
	GET_SEARCHED_PATIENT: `${baseDoc}/inbox/patient/retrieve`,
	TIMER: `${baseDoc}/timer`,
	INBOX: `${baseDoc}/inbox`,
	GET_CONSULTATION_BY_DATE: `${baseDoc}/consultation/filter`,
	GET_QUEUE_ELIGIBILITY: `${baseDoc}/queue/eligibility`,
	GET_PATIENT_DOCTOR_INFO: `${baseDoc}/patient`,
	NOTIFY_AWAY_PATIENT_ON_ADMIT: `${baseDoc}/alert/admit`,
	GET_NEXT_PATIENT_AUTOMATICALLY: `${baseDoc}/automate/next-patient`,
	POST_NOSHOW: `${baseDoc}/noshow`,
	GET_TP: `${baseDoc}/history/tp`,
	GET_ORDERS: `${baseDoc}/history/order`,
	GET_QUESTIONNAIRE: `${baseDoc}/history/questionnaire`,
	GET_HEALTHCHECK: `${baseDoc}/history/healthcheck`,
	GET_DOCTER_ACTIVITY: `${baseDoc}/drActivity`,
	STATUS_CHANGE: `${baseDoc}/status-change/admin`,
	USER_ACTIONS: `${baseDoc}/user/actions`,
	TECH_ISSUE: `${baseDoc}/techissue`,
	CREATE_PRESCRIPTION_LEAD: `${baseFunnel}/prescription-lead`,
	POST_CHAT_TREATMENT_PLAN: `${baseChat}/patient/chat-treatment-plan`,
	GET_CHAT_TREATMENT_PLANS: `${baseChat}/patient/:patientId/treatment-plans`,
	GET_CHAT_LATEST_TREATMENT_PLAN: `${baseChat}/patient/:patientId/treatment-plan-latest`,
	GET_CHAT_LATEST_TREATMENT_PLAN_WITH_DOCTOR: `${baseChat}/patient/:patientId/latest-treatment-plan-with-doctor`,
	EXCEEDED_ATTEMPTS: `${baseDoc}/patients/exceeded-attempts`,
	RESET_ATTEMPTS: `${baseDoc}/patient/reset-attempts`,
	SEND_SLACK_REPORT: `${baseDoc}/report/send`,
	SEND_SLACK_DETAILED_REPORT: `${baseDoc}/report/detailed/send`,
	SUBMIT_CONSENT_FORM: `${baseFunnel}/patient/consent-form`,
	GET_CONSENT_FORM_STATUS: `${baseFunnel}/patient/consent-form/status`,
	UPDATE_ZOHO_LEAD_MEMBER_STATUS: `${baseFunnel}/patient/updateZohoLeadMemberStatus`,
	GET_PATIENT_BY_EMAIL: `${baseFunnel}/patient/details`,
	GET_PATIENT_CONSENT_STATUS_BY_EMAIL: `${baseFunnel}/patient/consent-form/status-by-email`,
	LAST_DOCTOR_MESSAGE: `${baseZoho}/last-doctor-message`,
	GET_ZOHO_CONTACT_DETAILS: `${baseZoho}/contacts`,
	UPDATE_PATIENT_ZOHO_ID: `${baseDoc}/patient/update-zohoid`,
	GET_ZOHO_CONTACT_ID_BY_EMAIL: `${baseZoho}/contacts/id-by-email`,
	DOCTOR_LOGIN: `${baseDoc}/doctor/login`,
	DOCTOR_LOGOUT: `${baseDoc}/doctor/logout`,
	DOCTOR_SESSIONS: `${baseDoc}/doctor-sessions`,
	DOCTOR_STATUS: `${baseDoc}/doctor-status`,
	VERIFY_PIN: `${reportBase}/verify-pin`,
	MEDICINE_REGISTER: `${reportBase}/medicine-register`,
	GET_PATIENT_STATUS: `${baseDoc}/patient`,
	GET_BATCH_PATIENT_STATUS: `${baseDoc}/patients/status`,
	GET_PATIENT_DETAILED_STATUS: `${baseDoc}/patient`,
	DELETE_ANNOUNCEMENT: `${baseDoc}/announcement/delete`,
	UPDATE_ANNOUNCEMENT: `${baseDoc}/announcement/update`,
	GET_ANNOUNCEMENT: `${baseDoc}/announcement/retrieve`,
	CONFIRM_ANNOUNCEMENT: `${baseDoc}/announcement/confirm`,
	GET_AI_CHECK_RESPONSES: `${baseDoc}/ai-check/responses`,
	GET_PENDING_REQUESTS: `${baseAdmin}/requests/pending`,
	GET_DOCTOR_PENDING_REQUESTS: `${baseRequests}/pending-requests`,
	APPROVE_REQUEST: `${baseRequests}/request`,
	REJECT_REQUEST: `${baseRequests}/request`,
	// Quantity increase questionnaire endpoints
	POST_QUANTITY_INCREASE_QUESTIONNAIRE: `${baseFunnel}/patient/quantity-increase-questionnaire`,
	GET_QUANTITY_INCREASE_STATUS: `${baseFunnel}/patient/quantity-increase-questionnaire/status`,
	GET_PATIENT_QUANTITY_STATUS: `${baseFunnel}/patient/quantity-status`,
	POST_TRACKING_EVENT: `${reportBase}/track-event`,
	SEND_TRACKING_REPORT: `${reportBase}/send-tracking-report`,
};

export default routes;
